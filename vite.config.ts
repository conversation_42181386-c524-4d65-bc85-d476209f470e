import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
import { componentTagger } from "lovable-tagger";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  server: {
    host: "::",
    port: 5173,
  },
  plugins: [
    react(),
    mode === 'development' &&
    componentTagger(),
  ].filter(Boolean),
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  css: {
    postcss: false,
  },
  optimizeDeps: {
    exclude: [
      '@reown/appkit-controllers',
      '@reown/appkit-scaffold-ui',
      '@reown/appkit-ui',
      'text-encoding-utf-8',
      'qr.js',
      'react-lifecycles-compat',
      'rtcpeerconnection-shim',
      'warning'
    ],
    include: [
      'react/jsx-dev-runtime',
      '@solana/wallet-adapter-react',
      '@solana/wallet-adapter-react-ui',
      '@solana/wallet-adapter-wallets'
    ]
  },
  define: {
    global: 'globalThis',
  },
  build: {
    rollupOptions: {
      external: [
        '@reown/appkit-controllers',
        'qr.js/lib/QRCode',
        'qr.js/lib/ErrorCorrectLevel'
      ]
    }
  }
}));
